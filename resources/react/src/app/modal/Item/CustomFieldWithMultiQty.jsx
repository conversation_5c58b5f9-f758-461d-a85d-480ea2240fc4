import React, { useEffect, useMemo, useState } from "react";
import { <PERSON><PERSON>, Modal } from "react-bootstrap";
import Close from "../../../assets/images/svg/close";
import { useDispatch, useSelector } from "react-redux";
import { FormInput } from "../../../components/ui/Input";
import { errorToast } from "../../../store/actions/toastAction";
import { toastType } from "../../../constants";
import { calculateTotal, customToFixed } from "../../../shared/calculation";
import { useParams } from "react-router-dom";

const rowsPerPage = 20;

const CustomFieldWithMultiQty = ({
    show,
    handleClose,
    openClassificationModel,
    customFieldWithMultiQty,
    items,
    setItems,
    itemIndex,
    isInWord,
    changeTax
}) => {
    const { id } = useParams();
    const dispatch = useDispatch();
    const { configuration, company } = useSelector((state) => state);
    const saleConfiguration = configuration?.configuration;

    const [customFieldDetail, setCustomFieldDetail] = useState(customFieldWithMultiQty?.model_custom_fields?.length > 0 ? customFieldWithMultiQty?.model_custom_fields : customFieldWithMultiQty?.model_inventory_custom_fields || []);
    const [customFieldSelectInventoryDetail, setCustomFieldSelectInventoryDetail] = useState(customFieldWithMultiQty?.model_select_inventory_custom_fields || []);
    const [tempFieldValues, setTempFieldValues] = useState([]);
    const [tempMultiFieldValues, setTempMultiFieldValues] = useState([])
    const [currentPage, setCurrentPage] = useState(1);
    const [searchFilters, setSearchFilters] = useState({});
    const [customFieldLength, setCustomFieldLength] = useState(
    (items?.[itemIndex]?.custom_field_inventory_store?.length || 0) < 20 ? 20 : items?.[itemIndex]?.custom_field_inventory_store?.length
    );
    const [originalQuantities, setOriginalQuantities] = useState({});
    useEffect(() => {
        const initialValues = ((customFieldWithMultiQty?.custom_field_inventory_store?.length > 0 ? customFieldWithMultiQty?.custom_field_inventory_store : customFieldWithMultiQty?.model_select_inventory_custom_fields));
        setTempFieldValues(initialValues || []);
        setTempMultiFieldValues(items || []);

        // Track original quantities for edit mode only
        const originalQtys = {};
        const currentItemId = customFieldWithMultiQty?.item_master?.id || customFieldWithMultiQty?.selectedItem;

        // Check if we're in edit mode using URL parameter
        const isEditMode = Boolean(id);
        if (isEditMode) {
            items?.forEach((item, idx) => {
                const selectedItemId = item?.item_master?.id || item?.selectedItem;
                if (selectedItemId === currentItemId) {
                    const customFields = item?.model_select_inventory_custom_fields || item?.model_inventory_custom_fields || [];
                    customFields.forEach(entry => {
                        if (entry?.combination_id && entry?.sale_quantity > 0) {
                            originalQtys[entry.combination_id] = (originalQtys[entry.combination_id] || 0) + parseFloat(entry.sale_quantity);
                        }
                    });
                }
            });
        }

        setOriginalQuantities(originalQtys);
    }, [itemIndex, customFieldWithMultiQty?.custom_field_inventory_store, items, customFieldWithMultiQty]);

    useEffect(() => {
        setCustomFieldDetail(customFieldWithMultiQty?.model_custom_fields?.length > 0 ? customFieldWithMultiQty?.model_custom_fields : customFieldWithMultiQty?.model_inventory_custom_fields || []);
        setCustomFieldSelectInventoryDetail(customFieldWithMultiQty?.model_select_inventory_custom_fields || []);
    }, [customFieldWithMultiQty, show])

    useEffect(() => {
        const currentItemId = customFieldWithMultiQty?.item_master?.id || customFieldWithMultiQty?.selectedItem;
        const defaultCustomFields =
            customFieldWithMultiQty?.model_inventory_custom_fields?.length > 0 && customFieldWithMultiQty?.model_inventory_custom_fields?.[0]?.fields
                ? customFieldWithMultiQty?.model_inventory_custom_fields
                : customFieldWithMultiQty?.model_select_inventory_custom_fields?.length > 0 ? customFieldWithMultiQty?.model_select_inventory_custom_fields : customFieldWithMultiQty?.custom_field_inventory_store;

        const inventoryFields =
            customFieldWithMultiQty?.model_custom_fields ||
            customFieldWithMultiQty?.model_inventory_custom_fields ||
            [];
        setCustomFieldDetail(inventoryFields);

        const otherRowsSelectedCombinations = new Set();
        const currentRowSelectedCombinations = new Set();

        items?.forEach((itm, idx) => {
            const selectedItemId = itm?.item_master?.id || itm?.selectedItem;
            const selectedFields = itm?.model_inventory_custom_fields?.length > 0 && itm?.model_inventory_custom_fields?.[0].fields ? itm?.model_inventory_custom_fields : itm?.model_select_inventory_custom_fields || [];
            selectedFields.forEach(entry => {
                if (selectedItemId === currentItemId && entry?.is_selected) {
                    if (idx === itemIndex) {
                        currentRowSelectedCombinations.add(entry.combination_id);
                    } else {
                        otherRowsSelectedCombinations.add(entry.combination_id);
                    }
                }
            });
        });

        // Determine combinations for current item
        const baseCombinations =
            (customFieldWithMultiQty?.model_inventory_custom_fields?.length &&
            customFieldWithMultiQty?.model_inventory_custom_fields?.length > 0 && customFieldWithMultiQty?.model_inventory_custom_fields?.[0]?.fields
                ? customFieldWithMultiQty?.model_inventory_custom_fields
                : defaultCustomFields) || [];
        let finalCombinations = [];
        if(baseCombinations[0]?.fields) {
            finalCombinations = baseCombinations.map(entry => {
                const combinationId = entry?.combination_id;
                const isSelected = currentRowSelectedCombinations.has(combinationId);
                const isDisabled = otherRowsSelectedCombinations.has(combinationId);
                return {
                    ...entry,
                    is_selected: isSelected,
                    is_disabled: isDisabled,
                };
            });
        }else{
            finalCombinations = baseCombinations.map((combination, index) => {
            const combinationId = combination?.combination_id;
            const isSelected = currentRowSelectedCombinations?.has(combinationId);
            const isDisabled = otherRowsSelectedCombinations?.has(combinationId);

                return combination?.map(field => ({
                    ...field,
                    is_selected: isSelected,
                    is_disabled: isDisabled
                }));
            });
        }

        const updatedItems = [...items];
        updatedItems[itemIndex] = {
            ...updatedItems[itemIndex],
            model_inventory_custom_fields: finalCombinations,
        };
        setItems(updatedItems);
        setCustomFieldSelectInventoryDetail(finalCombinations);
    }, [customFieldWithMultiQty]);

    const getDisabledCombinationIds = () => {
        const currentItemId = customFieldWithMultiQty?.item_master?.id || customFieldWithMultiQty?.selectedItem;
        const disabledIds = [];

        items.forEach((itm, idx) => {
            if (idx !== itemIndex && itm?.selectedItem === currentItemId) {
                itm?.model_inventory_custom_fields?.forEach((row) => {
                    if (row?.is_selected) {
                        disabledIds.push(row.combination_id);
                    }
                });
            }
        });

        return disabledIds;
    };

    const getAdjustedAvailability = (currentItemIndex, items) => {
        const usedQuantities = {};
        const currentItemId = customFieldWithMultiQty?.item_master?.id || customFieldWithMultiQty?.selectedItem;
        const isEditMode = Boolean(id);

        items.forEach((item, idx) => {
            // skip current editing item
            if (idx === currentItemIndex) return;

            const selectedItemId = item?.item_master?.id || item?.selectedItem;

            // Check both model_select_inventory_custom_fields and model_inventory_custom_fields
            const customFields = item?.model_select_inventory_custom_fields || item?.model_inventory_custom_fields || [];

            customFields.forEach((entry) => {
                const combinationId = entry.combination_id;
                const saleQty = parseFloat(entry.sale_quantity) || 0;
                if (saleQty > 0) {
                    if (isEditMode && selectedItemId === currentItemId) {
                        const originalQty = originalQuantities[combinationId] || 0;
                        const adjustedQty = Math.max(0, saleQty - originalQty);
                        usedQuantities[combinationId] = (usedQuantities[combinationId] || 0) + adjustedQty;
                    } else {
                        usedQuantities[combinationId] = (usedQuantities[combinationId] || 0) + saleQty;
                    }
                }
            });
        });
        return usedQuantities;
    };

    const handleChange = (e, rowIndex, fieldId, colIndex, type = 'value') => {
        const { value } = e.target;
        const updatedTemp = [...tempFieldValues];
        // const existingStore = updatedItems[itemIndex]?.custom_field_inventory_store || [];

        while (updatedTemp.length <= rowIndex) updatedTemp.push([]);
        while ((updatedTemp[rowIndex] || []).length <= colIndex)
            updatedTemp[rowIndex].push({});

        // Ensure the cell exists
        const rowFields = tempFieldValues?.[rowIndex] || [];
        const quantityColIndex = rowFields?.findIndex(field => field?.quantity !== undefined);
        const existingCell = updatedTemp[rowIndex][colIndex] || {};
        // const quantityColIndex = rowFields.findIndex(field => field?.quantity !== undefined);

        // Update value or quantity based on type
        if (type === 'value') {
            updatedTemp[rowIndex][colIndex] = {
                ...existingCell,
                custom_field_id: fieldId,
                value,
                quantity: value ? updatedTemp[rowIndex][quantityColIndex]?.quantity || 1 : 0,
            };
        } else if (type === 'quantity') {
            // Update quantity for all cells in the row with same custom_field_id
            updatedTemp[rowIndex] = (updatedTemp[rowIndex] || []).map((cell) => {
                return {
                    ...cell,
                    quantity: Number(value),
                }}
            );
        }
        setTempFieldValues(updatedTemp);
    };

    const handleSaleQtyChange = (e, rowIndex, row) => {
        const { value } = e.target;
        const updatedItems = [...tempMultiFieldValues];
        const inventory = [...(updatedItems[itemIndex]?.model_select_inventory_custom_fields || [])];
        const adjustedAvailabilityMap = getAdjustedAvailability(itemIndex, tempMultiFieldValues);
        const availableQuantity = Math.max(0, row.available_quantity - (adjustedAvailabilityMap?.[row.combination_id] || 0))
        inventory[rowIndex] = {
            ...inventory[rowIndex],
            sale_quantity: parseFloat(availableQuantity) >= (parseFloat(value) || 0) ? (parseFloat(value) || 0) : parseFloat(availableQuantity),
        };

        updatedItems[itemIndex] = {
            ...updatedItems[itemIndex],
            model_select_inventory_custom_fields: inventory,
            model_inventory_custom_fields: inventory,
            custom_field_inventory_store: inventory,
        };
        getAdjustedAvailability(itemIndex, tempMultiFieldValues)
        setTempMultiFieldValues(updatedItems);
        // setItems(updatedItems);
        setCustomFieldSelectInventoryDetail(inventory);
    };

    const handleSearchChange = (e, key) => {
        setSearchFilters((prev) => ({ ...prev, [key]: e.target.value }));
        setCurrentPage(1);
    };
    const handleCheckboxChange = (e, rowIndex, row) => {
        const checked = e.target.checked;
        const updatedItems = [...tempMultiFieldValues];
        const fullList = [...customFieldSelectInventoryDetail];
        const adjustedAvailabilityMap = getAdjustedAvailability(itemIndex, tempMultiFieldValues);
        const availableQuantity = Math.max(0, row.available_quantity - (adjustedAvailabilityMap?.[row.combination_id] || 0))
        fullList[rowIndex] = { ...fullList[rowIndex], is_selected: checked, sale_quantity: checked ? availableQuantity : 0 };

        updatedItems[itemIndex] = {
            ...updatedItems[itemIndex],
            model_select_inventory_custom_fields: fullList,
            model_inventory_custom_fields: fullList,
            // custom_field_inventory_store: fullList,
        };
        setTempMultiFieldValues(updatedItems)
        setCustomFieldSelectInventoryDetail(fullList);
        // setItems(updatedItems);
    };

    const allRowsSelectedInvoice = useMemo(() => {
        return customFieldSelectInventoryDetail.map((row, rowIndex) => ({
            ...row,
            rowIndex,
            rowData: row.fields || [],
        }));
    }, [customFieldSelectInventoryDetail]);

    const filteredRowsSelectedInvoice = useMemo(() => {
    if (isInWord) return allRowsSelectedInvoice;

    return allRowsSelectedInvoice.filter((row) => {
    const fieldMatch = Object.entries(searchFilters).every(([key, filterValue]) => {
      if (!filterValue || !key) return true;

      // Match custom field
      const match = row.rowData?.some((cell) => {
        return String(cell.custom_field_id) === key &&
               (cell?.value ?? "").toLowerCase().includes(filterValue.toLowerCase());
      });

      // Match system fields
      if (key === "__purchase_rate") {
        return row.purchase_rate?.toString().includes(filterValue);
      }
      if (key === "__purchase_date") {
        return row.purchase_date?.toString().includes(filterValue);
      }
      if (key === "__available_qty") {
        return row.available_quantity?.toString().includes(filterValue);
      }
      if (key === "__sale_qty") {
        return row.sale_quantity?.toString().includes(filterValue);
      }

      return match;
    });

    return fieldMatch;
  });
    }, [searchFilters, allRowsSelectedInvoice, isInWord]);

    const paginatedRowsSelectedInvoice = useMemo(() => {
        return filteredRowsSelectedInvoice.slice(
            (currentPage - 1) * rowsPerPage,
            currentPage * rowsPerPage
        );
    }, [filteredRowsSelectedInvoice, currentPage]);

    const paginatedRowsManualInput = useMemo(() => {
        return Array.from({ length: customFieldLength })
            .slice((currentPage - 1) * rowsPerPage, currentPage * rowsPerPage)
            .map((_, indexOffset) => (currentPage - 1) * rowsPerPage + indexOffset);
    }, [customFieldLength, currentPage]);

    const handleCloseModel = () => {
        handleClose();
        if (
            saleConfiguration?.header?.is_change_gst_details &&
            company?.company?.is_gst_applicable
        ) {
            openClassificationModel();
        }
    };

    const handleSubmit = e => {
        e.preventDefault();
        const data = tempFieldValues;

        if (isInWord) {
            // Existing duplicate validation for isInWord
            for (let i = 0; i < data.length - 1; i++) {
                const isRowIEmpty = data[i].every(cell => !cell.value?.toString().trim());
                if (isRowIEmpty) continue;

                for (let j = i + 1; j < data.length; j++) {
                    const isRowJEmpty = data[j].every(cell => !cell.value?.toString().trim());
                    if (isRowJEmpty) continue;

                    const isDuplicate = data[i].every((cell, colIndex) => {
                        const valA = cell.value?.toString().trim();
                        const valB = data[j][colIndex]?.value?.toString().trim();
                        return valA === valB;
                    });

                    if (isDuplicate) {
                        const duplicateVal = data[i][0]?.value || "N/A";
                        return dispatch(
                            errorToast({
                                text: `Duplicate row found (Row ${i + 1} and Row ${
                                    j + 1
                                }) with value "${duplicateVal}"`,
                                type: toastType.ERROR,
                            })
                        );
                    }
                }
            }

            const updatedItems = [...items];
            updatedItems[itemIndex] = {
                ...updatedItems[itemIndex],
                custom_field_inventory_store: data,
            };

            const updatedItemsDetail = updatedItems.map((item, idx) => {
                // Check if custom field inventory exists for this item
                const hasCustomFieldInventory = item?.custom_field_inventory_store?.length > 0 ||
                                              item?.model_select_inventory_custom_fields?.length > 0;

                let item_quantity;

                if (hasCustomFieldInventory) {
                    // Update quantity only if custom field inventory exists
                    const inventoryStore = item?.custom_field_inventory_store || [];
                    const outWordinventoryStore = item?.model_select_inventory_custom_fields || [];

                    item_quantity = (isInWord &&
                    inventoryStore &&
                    (item?.model_custom_fields?.length > 0 || inventoryStore?.length > 0)
                        ? customToFixed(
                              inventoryStore.reduce((sum, group) => {
                                  return (
                                      sum +
                                      (Array.isArray(group) && group.length > 0
                                          ? group[0]?.quantity || 0
                                          : 0)
                                  );
                              }, 0),
                              item?.decimal_places
                          )
                        : item?.model_select_inventory_custom_fields?.length > 0
                        ? customToFixed(
                              outWordinventoryStore.reduce((sum, group) => {
                                  return sum + (parseFloat(group?.sale_quantity) || 0);
                              }, 0),
                              item?.decimal_places
                          )
                        : null) ?? 0.00;
                } else {
                    // Keep original quantity if no custom field inventory
                    item_quantity = item?.quantity || 0;
                }

                const updatedItem = {
                    ...item,
                    quantity: parseFloat(item_quantity || 0),
                    is_change_quantity: hasCustomFieldInventory,
                };

                if (hasCustomFieldInventory) {
                    const calculatedTotal = calculateTotal(updatedItem, false, changeTax, false);
                    updatedItem.total = calculatedTotal.total;
                    updatedItem.updatedTotal = calculatedTotal.total;
                    updatedItem.sgstValue = calculatedTotal.sgst;
                    updatedItem.cgstValue = calculatedTotal.sgst;
                    updatedItem.cessValue = calculatedTotal.cess;
                }

                return updatedItem;
            });

            setItems(updatedItemsDetail);
        } else {
            const updatedItems = tempMultiFieldValues.map((item, idx) => {
                // Check if custom field inventory exists for this item
                const hasCustomFieldInventory = item?.model_select_inventory_custom_fields?.length > 0 ||
                                              item?.model_inventory_custom_fields?.length > 0 ||
                                              item?.custom_field_inventory_store?.length > 0;

                let item_quantity;

                if (hasCustomFieldInventory) {
                    // Update quantity only if custom field inventory exists
                    const inventoryStore = item?.custom_field_inventory_store || [];
                    const outWordinventoryStore = item?.model_select_inventory_custom_fields || [];

                    item_quantity = (isInWord &&
                    inventoryStore &&
                    (item?.model_custom_fields?.length > 0 || inventoryStore?.length > 0)
                        ? customToFixed(
                              inventoryStore.reduce((sum, group) => {
                                  return (
                                      sum +
                                      (Array.isArray(group) && group.length > 0
                                          ? group[0]?.quantity || 0
                                          : 0)
                                  );
                              }, 0),
                              item?.decimal_places
                          )
                        : item?.model_select_inventory_custom_fields?.length > 0
                        ? customToFixed(
                              outWordinventoryStore.reduce((sum, group) => {
                                  return sum + (parseFloat(group?.sale_quantity) || 0);
                              }, 0),
                              item?.decimal_places
                          )
                        : null) ?? 0.00;
                } else {
                    // Keep original quantity if no custom field inventory
                    item_quantity = item?.quantity || 0;
                }

                const updatedItem = {
                    ...item,
                    quantity: parseFloat(item_quantity),
                    is_change_quantity: hasCustomFieldInventory,
                };

                if (hasCustomFieldInventory) {
                    const calculatedTotal = calculateTotal(updatedItem, false, changeTax, false);
                    updatedItem.total = calculatedTotal.total;
                    updatedItem.updatedTotal = calculatedTotal.total;
                    updatedItem.sgstValue = calculatedTotal.sgst;
                    updatedItem.cgstValue = calculatedTotal.sgst;
                    updatedItem.cessValue = calculatedTotal.cess;
                }

                return updatedItem;
            });

            setItems(updatedItems);
        }
        handleCloseModel();
    };

    const adjustedAvailabilityMap = getAdjustedAvailability(itemIndex, tempMultiFieldValues);

    return (
        <Modal show={show} onHide={handleCloseModel} centered className="custom-table-field-test-serial-modal">
            <div className="modal-header py-1 ps-4 pe-3">
                <button className="ms-auto btn btn-icon btn-sm btn-active-light-primary ms-2 close-btn" onClick={handleCloseModel}>
                    <Close />
                </button>
            </div>
            <Modal.Body className="px-4 py-2">
                <form onSubmit={handleSubmit}>
                <div className="w-100 overflow-auto">
                    <div className="custom-field-table-modal overflow-auto custom-table-field-test-serial mb-1">
                        <table className="w-100">
                            <thead>
                                <tr>
                                    {isInWord ? (
                                        <>
                                            <th>Sr No.</th>
                                            {customFieldDetail.map((field) => (
                                                <th key={field.id}>{field.label_name}</th>
                                            ))}
                                            <th>Qty</th>
                                        </>
                                    ) : (
                                        <>
                                            <th></th>
                                            <th className="w-30px">Sr No.</th>
                                            {customFieldDetail.map((field) => (
                                                <th key={field.id}>{field.label_name}</th>
                                            ))}
                                            <th>Available Qty</th>
                                            <th>Purchase Rate</th>
                                            <th>Purchase Date</th>
                                            <th>Sale Qty</th>
                                        </>
                                    )}
                                </tr>
                                {!isInWord && (
                                    <tr>
                                        <th></th>
                                        <th className="w-30px"></th>
                                        {customFieldDetail.map((field) => (
                                            <th key={`search-${field.id}`}>
                                                <FormInput
                                                    type="text"
                                                    className="form-control search-box h-26px"
                                                    placeholder="Search"
                                                    value={searchFilters[field.id] || ""}
                                                    onChange={(e) => handleSearchChange(e, field.id)}
                                                />
                                            </th>
                                        ))}
                                        <th>
                                            <FormInput
                                                type="text"
                                                className="form-control search-box h-26px"
                                                placeholder="Available Qty"
                                                value={searchFilters["__available_qty"] || ""}
                                                onChange={(e) => handleSearchChange(e, "__available_qty")}
                                            />
                                        </th>
                                        <th>
                                            <FormInput
                                                type="text"
                                                className="form-control search-box h-26px"
                                                placeholder="Rate"
                                                value={searchFilters["__purchase_rate"] || ""}
                                                onChange={(e) => handleSearchChange(e, "__purchase_rate")}
                                            />
                                        </th>
                                        <th>
                                            <FormInput
                                                type="text"
                                                className="form-control search-box h-26px"
                                                placeholder="Date"
                                                value={searchFilters["__purchase_date"] || ""}
                                                onChange={(e) => handleSearchChange(e, "__purchase_date")}
                                            />
                                        </th>
                                        <th>
                                            <FormInput
                                                type="text"
                                                className="form-control search-box h-26px"
                                                placeholder="Sale Qty"
                                                value={searchFilters["__sale_qty"] || ""}
                                                onChange={(e) => handleSearchChange(e, "__sale_qty")}
                                            />
                                        </th>
                                    </tr>
                                )}
                            </thead>
                            <tbody>
                                {isInWord
                                    ? paginatedRowsManualInput.map((rowIndex) => {
                                        const rowFields = tempFieldValues?.[rowIndex] || [];
                                        const quantityColIndex = rowFields.findIndex((field) => field?.quantity !== undefined);
                                        return (
                                            <>
                                                <tr key={rowIndex}>
                                                    <td className="text-start w-30px">{rowIndex + 1}</td>
                                                    {customFieldDetail.map((field, colIndex) => {
                                                        return (
                                                            <td key={field.id}>
                                                                <FormInput
                                                                    type="text"
                                                                    value={tempFieldValues?.[rowIndex]?.[colIndex]?.value || ""}
                                                                    placeholder={field.label_name}
                                                                    onChange={(e) => handleChange(e, rowIndex, field.id, colIndex, "value")}
                                                                />
                                                            </td>
                                                        );
                                                    })}
                                                    <td>
                                                        <FormInput
                                                            type="number"
                                                            required={rowFields?.some(field => field?.value?.toString().trim())}
                                                            value={rowFields?.[quantityColIndex]?.quantity}
                                                            onChange={(e) => handleChange(e, rowIndex, customFieldDetail[0]?.id, 0, "quantity")}
                                                            min="1"
                                                            step="any"
                                                            onKeyDown={(e) => {
                                                                if (e.key === "-" || e.key === "e") {
                                                                e.preventDefault();
                                                                }
                                                            }}
                                                        />
                                                    </td>
                                                </tr>
                                            </>
                                    )})
                                    : paginatedRowsSelectedInvoice.map((row, index) => {
                                        const available_quantity = Math.max(0, row.available_quantity - (adjustedAvailabilityMap?.[row.combination_id] || 0));

                                        // disable checkbox if not enough quantity or it's globally disabled
                                        const disableForQty = available_quantity <= 0;

                                        // isChecked = true only if enough quantity & selected
                                        const isChecked = row.is_selected && available_quantity > 0;

                                        // disable FormInput if not selected or not enough available quantity
                                        const disableSaleQty = !(row.is_selected && (adjustedAvailabilityMap?.[row.combination_id] ? available_quantity >= row.sale_quantity : true));
                                        return (
                                            <>
                                            <tr key={row.rowIndex}>
                                            <td className="text-start">
                                                <input
                                                    type="checkbox"
                                                    className="form-check-input"
                                                    // checked={isChecked}
                                                    checked={row?.sale_quantity >= available_quantity ? true : false}
                                                    onChange={(e) => handleCheckboxChange(e, row.rowIndex, row)}
                                                    disabled={disableForQty}
                                                />
                                            </td>
                                            <td className="w-30px text-start">{row.rowIndex + 1}</td>
                                            {customFieldDetail.map((field) => {
                                                const matchedField = row.rowData.find(
                                                    (f) => f.custom_field_id === field.id
                                                );
                                                return <td key={field.id} className="text-start">{matchedField?.value || ""}</td>;
                                            })}
                                            <td className="text-start">{available_quantity}</td>
                                            <td className="text-start">{row.purchase_rate}</td>
                                            <td className="text-start">{row.purchase_date}</td>
                                            <td>
                                                <FormInput
                                                    type="number"
                                                    min="0"
                                                    step="any"
                                                    onKeyDown={(e) => {
                                                        if (e.key === "-" || e.key === "e") {
                                                        e.preventDefault();
                                                        }
                                                    }}
                                                    value={parseFloat(row.sale_quantity) || ""}
                                                    onChange={(e) => handleSaleQtyChange(e, row.rowIndex, row)}
                                                    // disabled={disableSaleQty}
                                                />
                                            </td>
                                            </tr>
                                            </>
                                    )})}
                            </tbody>
                        </table>
                    </div>
                    {isInWord && (
                        <button
                            className="btn btn-primary btn-sm mt-2 mb-1"
                            type="button"
                            onClick={() => setCustomFieldLength((prev) => prev + 1)}
                            style={{ padding: "4px 14px 4px 10px", fontSize: "11px" }}
                        >
                            ADD <span className="font-semibold fs-4 lh-1">+</span>
                        </button>
                    )}

                    <div className="overflow-auto justify-content-center d-flex mb-2">
                        <nav>
                            <ul className="pagination cursor-pointer">
                                <li className={`page-item ${currentPage === 1 ? "disabled" : ""}`} onClick={() => currentPage > 1 && setCurrentPage((p) => p - 1)}>
                                    <span className="page-link">&lsaquo;</span>
                                </li>
                                {Array.from({
                                    length: Math.ceil((isInWord ? customFieldLength : filteredRowsSelectedInvoice.length) / rowsPerPage),
                                }).map((_, idx) => (
                                    <li
                                        key={idx}
                                        className={`page-item ${currentPage === idx + 1 ? "active" : ""}`}
                                        onClick={() => setCurrentPage(idx + 1)}
                                    >
                                        <span className="page-link">{idx + 1}</span>
                                    </li>
                                ))}
                                <li
                                    className={`page-item ${currentPage === Math.ceil((isInWord ? customFieldLength : filteredRowsSelectedInvoice.length) / rowsPerPage) ? "disabled" : ""}`}
                                    onClick={() => currentPage < Math.ceil((isInWord ? customFieldLength : filteredRowsSelectedInvoice.length) / rowsPerPage) && setCurrentPage((p) => p + 1)}
                                >
                                    <span className="page-link">&rsaquo;</span>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>

                <div className="d-flex gap-3">
                    <Button variant="primary" type="submit" className="btn-sm fs-13">Save</Button>
                    <Button variant="secondary" onClick={handleCloseModel} className="btn-sm fs-13">Close</Button>
                </div>
                </form>
            </Modal.Body>
        </Modal>
    );
};

export default CustomFieldWithMultiQty;
